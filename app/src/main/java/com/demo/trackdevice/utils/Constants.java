package com.demo.trackdevice.utils;

public class Constants {
    public static String FREE_SERVERS = "";
    public static String PREMIUM_SERVERS = "";
    public static String ONECONNECT_KEY = "aX.8R1AznMRI2zs4KYSBv6xekvxLgQpXKg4WgU19mBvGfrkARP";

    // Demo VPN servers for testing (will show connection process but may not establish actual VPN)
    public static final String FALLBACK_FREE_SERVERS = "[\n" +
            "  {\n" +
            "    \"serverName\": \"United States\",\n" +
            "    \"flag_url\": \"🇺🇸\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ******* 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\nverb 3\\ncipher AES-256-CBC\\nauth SHA256\\nconnect-timeout 10\\nconnect-retry 2\",\n" +
            "    \"vpnUserName\": \"demo\",\n" +
            "    \"vpnPassword\": \"demo\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Germany\",\n" +
            "    \"flag_url\": \"🇩🇪\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ******* 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\nverb 3\\ncipher AES-256-CBC\\nauth SHA256\\nconnect-timeout 10\\nconnect-retry 2\",\n" +
            "    \"vpnUserName\": \"demo\",\n" +
            "    \"vpnPassword\": \"demo\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Canada\",\n" +
            "    \"flag_url\": \"🇨🇦\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ******* 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\nverb 3\\ncipher AES-256-CBC\\nauth SHA256\\nconnect-timeout 10\\nconnect-retry 2\",\n" +
            "    \"vpnUserName\": \"demo\",\n" +
            "    \"vpnPassword\": \"demo\"\n" +
            "  }\n" +
            "]";
}