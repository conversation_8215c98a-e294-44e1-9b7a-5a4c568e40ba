package com.demo.trackdevice.activity;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.net.VpnService;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.PopupMenu;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.R;
import com.demo.trackdevice.utils.SafetyCalculation;
import com.demo.trackdevice.ads.AdsCommon;
import com.demo.trackdevice.ads.MyApplication;
import com.demo.trackdevice.ads.VpnAdManager;
import com.demo.trackdevice.vpn.VpnManager;
import com.demo.trackdevice.vpn.adapter.CountrySpinnerAdapter;
import com.demo.trackdevice.vpn.model.Countries;
import com.github.lzyzsd.circleprogress.CircleProgress;

import java.util.List;

public class MainActivity extends AppCompatActivity implements VpnManager.VpnConnectionListener {
    private static final int VPN_REQUEST_CODE = 1001;

    AppPreferences appPreferences;
    VpnManager vpnManager;
    VpnAdManager vpnAdManager;

    // VPN Controls
    Spinner spinnerCountries;
    TextView tvVpnConnectionStatus;
    Button btnSecure;
    View statusIndicator;
    CountrySpinnerAdapter countryAdapter;
    Countries selectedCountry;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().getDecorView().setSystemUiVisibility(1280);
        setWindowFlag(this, 67108864, false);
        getWindow().setStatusBarColor(0);
        setContentView(R.layout.activity_main);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        final ImageView imageView = (ImageView) findViewById(R.id.menu_popup);
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                PopupMenu popupMenu = new PopupMenu(MainActivity.this, imageView);
                popupMenu.getMenuInflater().inflate(R.menu.main_home, popupMenu.getMenu());
                popupMenu.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
                    @Override
                    public boolean onMenuItemClick(MenuItem menuItem) {
                        if (menuItem.getTitle().equals("Rate App")) {
                            MainActivity.this.startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + MainActivity.this.getApplicationContext().getPackageName())));
                            return true;
                        } else if (menuItem.getTitle().equals("Share App")) {
                            Intent intent = new Intent("android.intent.action.SEND");
                            intent.setFlags(268435456);
                            intent.setType("text/plain");
                            intent.putExtra("android.intent.extra.TEXT", "Hey, Download best app : https://play.google.com/store/apps/details?id=" + MainActivity.this.getApplicationContext().getPackageName());
                            MainActivity.this.startActivity(intent);
                            return true;
                        } else if (!menuItem.getTitle().equals("Privacy Policy")) {
                            return true;
                        } else {
                            Intent intentPrivacy = new Intent(Intent.ACTION_VIEW, Uri.parse(MyApplication.PrivacyPolicy));
                            intentPrivacy.setPackage("com.android.chrome");
                            startActivity(intentPrivacy);
                            return true;
                        }
                    }
                });
                popupMenu.show();
            }
        });
        this.appPreferences = new AppPreferences(getApplicationContext());
        this.vpnManager = new VpnManager(this);
        this.vpnManager.setConnectionListener(this);
        this.vpnAdManager = new VpnAdManager(this);

        // Initialize VPN controls
        initVpnControls();
        findViewById(R.id.RL_Traking).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Permission_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        findViewById(R.id.RL_Network).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Network_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        findViewById(R.id.RL_Device_Info).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Device_Info_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });

        // Device info is now replaced with VPN controls
    }

    private void initVpnControls() {
        try {
            // Initialize VPN UI components
            spinnerCountries = findViewById(R.id.spinner_countries);
            tvVpnConnectionStatus = findViewById(R.id.tv_vpn_connection_status);
            btnSecure = findViewById(R.id.btn_secure);
            statusIndicator = findViewById(R.id.status_indicator);

            // Add speed monitoring TextViews
            TextView textDownloading = findViewById(R.id.downloading);
            TextView textUploading = findViewById(R.id.uploading);

            if (textDownloading != null) {
                textDownloading.setText("0.0 kB/s");
            }
            if (textUploading != null) {
                textUploading.setText("0.0 kB/s");
            }

            // Register broadcast receiver for VPN status
            registerVpnBroadcastReceiver();

            // Setup country spinner with available servers
            setupCountrySpinner();

            // Setup secure button click listener
            btnSecure.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        Animation clickAnimation = AnimationUtils.loadAnimation(MainActivity.this, R.anim.button_click_animation);
                        v.startAnimation(clickAnimation);

                        if (vpnManager.isVpnConnected()) {
                            disconnectVpn();
                        } else {
                            // Connect using selected country from spinner
                            connectVpn();
                        }
                    } catch (Exception e) {
                        Toast.makeText(MainActivity.this, "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                }
            });

            // Load saved server if any
            Countries savedServer = vpnManager.getSelectedServer();
            if (savedServer != null && savedServer.getCountry() != null && !savedServer.getCountry().isEmpty()) {
                selectedCountry = savedServer;
                updateSpinnerSelection();
            }

            updateVpnUI();
        } catch (Exception e) {
            Toast.makeText(this, "Error initializing VPN controls: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void setupCountrySpinner() {
        try {
            // Get available servers from VPN manager
            List<Countries> servers = vpnManager.getAvailableServers();
            if (servers == null || servers.isEmpty()) {
                Toast.makeText(this, "No VPN servers available", Toast.LENGTH_SHORT).show();
                return;
            }

            // Create and set adapter for the spinner
            countryAdapter = new CountrySpinnerAdapter(this, servers);
            spinnerCountries.setAdapter(countryAdapter);

            // Set spinner item selection listener
            spinnerCountries.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    selectedCountry = servers.get(position);
                    Log.d("MainActivity", "Country selected: " + selectedCountry.getCountry());
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // Do nothing
                }
            });

            // Set default selection to first server if no saved server
            if (selectedCountry == null && !servers.isEmpty()) {
                selectedCountry = servers.get(0);
                spinnerCountries.setSelection(0);
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up country spinner: " + e.getMessage());
            Toast.makeText(this, "Error loading VPN servers", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateSpinnerSelection() {
        try {
            if (countryAdapter != null && selectedCountry != null && spinnerCountries != null) {
                // Find the position of the selected country in the adapter
                for (int i = 0; i < countryAdapter.getCount(); i++) {
                    Countries country = (Countries) countryAdapter.getItem(i);
                    if (country != null && country.getCountry().equals(selectedCountry.getCountry())) {
                        spinnerCountries.setSelection(i);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error updating spinner selection: " + e.getMessage());
        }
    }

    private void showServerSelection() {
        // Create a simple server selection dialog
        List<Countries> servers = vpnManager.getAvailableServers();
        if (servers == null || servers.isEmpty()) {
            Toast.makeText(this, "No VPN servers available", Toast.LENGTH_SHORT).show();
            return;
        }

        String[] serverNames = new String[servers.size()];
        for (int i = 0; i < servers.size(); i++) {
            serverNames[i] = servers.get(i).getCountry();
        }

        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Select VPN Server");
        builder.setItems(serverNames, (dialog, which) -> {
            selectedCountry = servers.get(which);
            connectVpn();
        });
        builder.show();
    }

    private void connectVpn() {
        if (selectedCountry != null) {
            // Show ad before connecting to VPN
            showVpnAdAndConnect();
        } else {
            Toast.makeText(this, "Please select a server from the dropdown first", Toast.LENGTH_SHORT).show();
        }
    }

    private void showVpnAdAndConnect() {
        vpnAdManager.showVpnAd(new VpnAdManager.VpnAdCallback() {
            @Override
            public void onAdCompleted() {
                // Ad completed, proceed with VPN connection
                proceedWithVpnConnection();
            }

            @Override
            public void onAdSkipped() {
                // Ad skipped, proceed with VPN connection
                proceedWithVpnConnection();
            }

            @Override
            public void onAdFailed() {
                // Ad failed, proceed with VPN connection anyway
                proceedWithVpnConnection();
            }
        });
    }

    private void proceedWithVpnConnection() {
        if (selectedCountry == null) {
            Toast.makeText(this, "Please select a server first", Toast.LENGTH_SHORT).show();
            return;
        }

        Intent intent = VpnService.prepare(this);
        if (intent != null) {
            startActivityForResult(intent, VPN_REQUEST_CODE);
        } else {
            // Already have permission, start VPN using FastPro logic
            startVpnConnection();
        }
    }

    private void startVpnConnection() {
        try {
            // Use the EXACT FastPro VPN connection logic
            android.util.Log.d("MainActivity", "Starting VPN connection to: " + selectedCountry.getCountry());
            android.util.Log.d("MainActivity", "VPN Config: " + selectedCountry.getOvpn());
            android.util.Log.d("MainActivity", "Username: " + selectedCountry.getOvpnUserName());
            android.util.Log.d("MainActivity", "Password: " + selectedCountry.getOvpnUserPassword());

            // Save the selected server using FastPro's ActiveServer
            com.demo.trackdevice.vpn.utils.ActiveServer.saveServer(selectedCountry, this);

            // Update UI to connecting state first
            updateVpnConnectingUI();

            // Start VPN using OpenVpnApi exactly like FastPro does - NO DELAY
            top.oneconnectapi.app.OpenVpnApi.startVpn(
                this,
                selectedCountry.getOvpn(),
                selectedCountry.getCountry(),
                selectedCountry.getOvpnUserName(),
                selectedCountry.getOvpnUserPassword()
            );

            android.util.Log.d("MainActivity", "OpenVpnApi.startVpn() called successfully");

        } catch (android.os.RemoteException e) {
            android.util.Log.e("MainActivity", "RemoteException starting VPN: " + e.getMessage());
            Toast.makeText(this, "Failed to start VPN: " + e.getMessage(), Toast.LENGTH_LONG).show();
            updateVpnDisconnectedUI();
        } catch (Exception e) {
            android.util.Log.e("MainActivity", "Error starting VPN: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Failed to start VPN: " + e.getMessage(), Toast.LENGTH_LONG).show();
            updateVpnDisconnectedUI();
        }
    }

    private void disconnectVpn() {
        try {
            // Use FastPro disconnect logic - create a new OpenVPNThread and stop it
            top.oneconnectapi.app.core.OpenVPNThread vpnThread = new top.oneconnectapi.app.core.OpenVPNThread();
            vpnThread.stop();

            // Update UI
            updateVpnDisconnectedUI();

            Toast.makeText(this, "VPN Disconnected", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            android.util.Log.e("MainActivity", "Error disconnecting VPN: " + e.getMessage());
            // Update UI anyway
            updateVpnDisconnectedUI();
        }
    }

    private void registerVpnBroadcastReceiver() {
        // Use LocalBroadcastManager like FastPro does
        android.content.IntentFilter filter = new android.content.IntentFilter("connectionState");

        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(this).registerReceiver(
            new android.content.BroadcastReceiver() {
                @Override
                public void onReceive(android.content.Context context, android.content.Intent intent) {
                    try {
                        String state = intent.getStringExtra("state");
                        String duration = intent.getStringExtra("duration");
                        String lastPacketReceive = intent.getStringExtra("lastPacketReceive");
                        String byteIn = intent.getStringExtra("byteIn");
                        String byteOut = intent.getStringExtra("byteOut");

                        android.util.Log.d("MainActivity", "VPN Status: " + state);

                        if (state != null) {
                            updateVpnStatus(state);
                        }

                        if (duration == null) duration = "00:00:00";
                        if (lastPacketReceive == null) lastPacketReceive = "0";
                        if (byteIn == null) byteIn = " ";
                        if (byteOut == null) byteOut = " ";

                        updateConnectionStats(duration, lastPacketReceive, byteIn, byteOut);

                    } catch (Exception e) {
                        android.util.Log.e("MainActivity", "Error processing VPN status: " + e.getMessage());
                    }
                }
            }, filter);
    }

    private void updateConnectionStats(String duration, String lastPacketReceive, String byteIn, String byteOut) {
        runOnUiThread(() -> {
            try {
                TextView textDownloading = findViewById(R.id.downloading);
                TextView textUploading = findViewById(R.id.uploading);

                // FastPro format: speed data comes as "bytes-speed" format
                if (textDownloading != null && byteIn != null && !byteIn.trim().isEmpty()) {
                    if (byteIn.contains("-")) {
                        String[] parts = byteIn.split("-");
                        if (parts.length > 1) {
                            textDownloading.setText("⬇ " + parts[1]);
                        }
                    } else {
                        textDownloading.setText("⬇ " + byteIn);
                    }
                }

                if (textUploading != null && byteOut != null && !byteOut.trim().isEmpty()) {
                    if (byteOut.contains("-")) {
                        String[] parts = byteOut.split("-");
                        if (parts.length > 1) {
                            textUploading.setText("⬆ " + parts[1]);
                        }
                    } else {
                        textUploading.setText("⬆ " + byteOut);
                    }
                }

                android.util.Log.d("MainActivity", "Speed - Down: " + byteIn + ", Up: " + byteOut);

            } catch (Exception e) {
                android.util.Log.e("MainActivity", "Error updating connection stats: " + e.getMessage());
            }
        });
    }

    private void updateVpnStatus(String status) {
        runOnUiThread(() -> {
            switch (status) {
                case "CONNECTED":
                    updateVpnConnectedUI();
                    break;
                case "DISCONNECTED":
                case "USERPAUSE":
                case "NONETWORK":
                    updateVpnDisconnectedUI();
                    break;
                case "CONNECTING":
                case "WAIT":
                case "AUTH":
                case "GET_CONFIG":
                case "ASSIGN_IP":
                case "RECONNECTING":
                    updateVpnConnectingUI();
                    break;
            }
        });
    }

    private void updateVpnConnectedUI() {
        String serverName = (selectedCountry != null && selectedCountry.getCountry() != null) ? selectedCountry.getCountry() : "Unknown Server";
        tvVpnConnectionStatus.setText("✅ Connected to " + serverName);
        tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        btnSecure.setText("🔓 DISCONNECT");
        btnSecure.setBackgroundResource(R.drawable.disconnect_button_gradient);
        statusIndicator.setBackgroundResource(R.drawable.status_indicator_connected);
        btnSecure.setEnabled(true);
    }

    private void updateVpnConnectingUI() {
        tvVpnConnectionStatus.setText("⏳ Connecting...");
        tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
        btnSecure.setText("⏳ CONNECTING...");
        btnSecure.setEnabled(false);
        statusIndicator.setBackgroundResource(R.drawable.status_indicator_connecting);
    }

    private void updateVpnDisconnectedUI() {
        tvVpnConnectionStatus.setText("❌ Not Connected");
        tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        btnSecure.setText("🔒 SECURE NOW");
        btnSecure.setBackgroundResource(R.drawable.secure_button_gradient);
        statusIndicator.setBackgroundResource(R.drawable.status_indicator_disconnected);
        btnSecure.setEnabled(true);

        // Reset speed display
        TextView textDownloading = findViewById(R.id.downloading);
        TextView textUploading = findViewById(R.id.uploading);
        if (textDownloading != null) textDownloading.setText("⬇ 0.0 kB/s");
        if (textUploading != null) textUploading.setText("⬆ 0.0 kB/s");
    }

    private void updateVpnUI() {
        if (vpnManager.isVpnConnected()) {
            updateVpnConnectedUI();
        } else {
            updateVpnDisconnectedUI();
        }
    }

    public static void setWindowFlag(Activity activity, int i, boolean z) {
        Window window = activity.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        if (z) {
            attributes.flags = i | attributes.flags;
        } else {
            attributes.flags = (~i) & attributes.flags;
        }
        window.setAttributes(attributes);
    }

    @Override
    public void onResume() {
        super.onResume();
        updateVpnUI();
    }

    @Override
    public void onBackPressed() {
        ExitDialog();
    }

    private void ExitDialog() {

        final Dialog dialog = new Dialog(MainActivity.this, R.style.DialogTheme);
        dialog.setContentView(R.layout.popup_exit_dialog);
        dialog.setCancelable(false);

        RelativeLayout no = (RelativeLayout) dialog.findViewById(R.id.no);
        RelativeLayout rate = (RelativeLayout) dialog.findViewById(R.id.rate);
        RelativeLayout yes = (RelativeLayout) dialog.findViewById(R.id.yes);

        no.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        rate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final String rateapp = getPackageName();
                Intent intent1 = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + rateapp));
                startActivity(intent1);
            }
        });

        yes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                finish();
                System.exit(0);
                //Intent intent = new Intent(AppMainHomeActivity.this, AppThankYouActivity.class);
                //intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                //AdsCommon.InterstitialAd(AppMainHomeActivity.this, intent);
            }
        });

        dialog.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == VPN_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                // Permission granted, start VPN connection
                Log.d("MainActivity", "VPN permission granted, starting connection");
                startVpnConnection();
            } else {
                Log.w("MainActivity", "VPN permission denied by user");
                Toast.makeText(this, "VPN permission is required to connect", Toast.LENGTH_LONG).show();
            }
        }
    }

    // VpnManager.VpnConnectionListener implementation
    @Override
    public void onConnected() {
        runOnUiThread(() -> {
            // Stop any animations
            statusIndicator.clearAnimation();
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Connected Successfully! 🔒 Check your IP address now.", Toast.LENGTH_LONG).show();
            android.util.Log.d("MainActivity", "VPN Connected successfully to: " + (selectedCountry != null ? selectedCountry.getCountry() : "Unknown"));
        });
    }

    @Override
    public void onDisconnected() {
        runOnUiThread(() -> {
            // Stop any animations
            statusIndicator.clearAnimation();
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Disconnected 🔓", Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public void onConnecting() {
        runOnUiThread(() -> {
            tvVpnConnectionStatus.setText("⏳ Connecting...");
            tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            btnSecure.setText("⏳ CONNECTING...");
            btnSecure.setEnabled(false);
            statusIndicator.setBackgroundResource(R.drawable.status_indicator_connecting);

            // Add pulse animation to status indicator while connecting
            Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);
            statusIndicator.startAnimation(pulseAnimation);
        });
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> {
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Connection Failed: " + error, Toast.LENGTH_LONG).show();
            android.util.Log.e("MainActivity", "VPN Connection Error: " + error);
        });
    }

    @Override
    public void onStatusChanged(String status) {
        runOnUiThread(() -> {
            android.util.Log.d("MainActivity", "VPN Status Changed: " + status);
            switch (status) {
                case "CONNECTED":
                    onConnected();
                    break;
                case "DISCONNECTED":
                case "USERPAUSE":
                case "NONETWORK":
                    onDisconnected();
                    break;
                case "CONNECTING":
                case "WAIT":
                case "AUTH":
                case "GET_CONFIG":
                case "ASSIGN_IP":
                case "RECONNECTING":
                    onConnecting();
                    break;
            }
        });
    }

    @Override
    public void onSpeedUpdate(String downloadSpeed, String uploadSpeed) {
        runOnUiThread(() -> {
            // Update speed display if you have UI elements for it
            android.util.Log.d("MainActivity", "Speed - Down: " + downloadSpeed + ", Up: " + uploadSpeed);
        });
    }

    @Override
    public void onConnectionStats(String duration, String byteIn, String byteOut) {
        runOnUiThread(() -> {
            // Update connection stats if you have UI elements for it
            android.util.Log.d("MainActivity", "Stats - Duration: " + duration + ", In: " + byteIn + ", Out: " + byteOut);
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (vpnAdManager != null) {
            vpnAdManager.cleanup();
        }
    }

}
